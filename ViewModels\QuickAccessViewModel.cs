using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using Prism.Commands;
using OxyPlot;
using OxyPlot.Series;
using OxyPlot.Axes;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للوصول السريع - عرض السائقين المتواجدين بالميدان
    /// </summary>
    public class QuickAccessViewModel : INotifyPropertyChanged
    {
        private readonly QuickAccessService _quickAccessService;
        private DispatcherTimer _refreshTimer;
        
        private ObservableCollection<ActiveDriverInfo> _activeDrivers;
        private ObservableCollection<ActiveDriverInfo> _filteredDrivers;
        private QuickAccessStatistics _statistics;
        private QuickAccessFilter _filter;
        private bool _isLoading;
        private string _searchText;
        private bool _showOnlyInField = true;
        private bool _showEndingToday;
        private bool _showEndingTomorrow;
        private string _selectedTab = "Dashboard";
        private bool _isDashboardVisible = true;
        private bool _isQuickAccessVisible = false;
        private PlotModel _monthlyVisitsChart;
        private PlotModel _driversStatusChart;

        public QuickAccessViewModel()
        {
            _quickAccessService = new QuickAccessService();
            _filter = new QuickAccessFilter();
            
            ActiveDrivers = new ObservableCollection<ActiveDriverInfo>();
            FilteredDrivers = new ObservableCollection<ActiveDriverInfo>();
            Statistics = new QuickAccessStatistics();

            InitializeCommands();
            InitializeAutoRefresh();
            InitializeCharts();

            // تحميل البيانات عند بدء التشغيل
            _ = LoadDataAsync();
        }

        #region Properties

        public ObservableCollection<ActiveDriverInfo> ActiveDrivers
        {
            get => _activeDrivers;
            set
            {
                _activeDrivers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ActiveDriverInfo> FilteredDrivers
        {
            get => _filteredDrivers;
            set
            {
                _filteredDrivers = value;
                OnPropertyChanged();
            }
        }

        public QuickAccessStatistics Statistics
        {
            get => _statistics;
            set
            {
                _statistics = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                _filter.SearchText = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowOnlyInField
        {
            get => _showOnlyInField;
            set
            {
                _showOnlyInField = value;
                _filter.ShowOnlyInField = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowEndingToday
        {
            get => _showEndingToday;
            set
            {
                _showEndingToday = value;
                _filter.ShowEndingToday = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowEndingTomorrow
        {
            get => _showEndingTomorrow;
            set
            {
                _showEndingTomorrow = value;
                _filter.ShowEndingTomorrow = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public string SelectedTab
        {
            get => _selectedTab;
            set
            {
                _selectedTab = value;
                OnPropertyChanged();
                UpdateTabVisibility();
            }
        }

        public bool IsDashboardVisible
        {
            get => _isDashboardVisible;
            set
            {
                _isDashboardVisible = value;
                OnPropertyChanged();
            }
        }

        public bool IsQuickAccessVisible
        {
            get => _isQuickAccessVisible;
            set
            {
                _isQuickAccessVisible = value;
                OnPropertyChanged();
            }
        }

        public string LastUpdateTime { get; private set; } = DateTime.Now.ToString("HH:mm:ss");

        public PlotModel MonthlyVisitsChart
        {
            get => _monthlyVisitsChart;
            set
            {
                _monthlyVisitsChart = value;
                OnPropertyChanged();
            }
        }

        public PlotModel DriversStatusChart
        {
            get => _driversStatusChart;
            set
            {
                _driversStatusChart = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; private set; }
        public ICommand SwitchTabCommand { get; private set; }
        public ICommand ClearFiltersCommand { get; private set; }
        public ICommand SelectAllCommand { get; private set; }
        public ICommand ClearSelectionCommand { get; private set; }

        private void InitializeCommands()
        {
            RefreshCommand = new DelegateCommand(async () => await LoadDataAsync());
            SwitchTabCommand = new DelegateCommand<string>(SwitchTab);
            ClearFiltersCommand = new DelegateCommand(ClearFilters);
            SelectAllCommand = new DelegateCommand(SelectAll);
            ClearSelectionCommand = new DelegateCommand(ClearSelection);
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل البيانات من قاعدة البيانات
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحميل بيانات الوصول السريع...");

                // تأخير قصير لضمان ظهور مؤشر التحميل
                await Task.Delay(300);

                // تحميل السائقين النشطين
                var activeDrivers = await _quickAccessService.GetActiveDriversInFieldAsync();

                // تحميل الإحصائيات
                var statistics = await _quickAccessService.GetQuickAccessStatisticsAsync();

                // تحديث الواجهة في الخيط الرئيسي
                App.Current.Dispatcher.Invoke(() =>
                {
                    ActiveDrivers.Clear();
                    foreach (var driver in activeDrivers)
                    {
                        ActiveDrivers.Add(driver);
                    }

                    System.Diagnostics.Debug.WriteLine($"🔍 تم إضافة {ActiveDrivers.Count} سائق إلى ActiveDrivers");

                    Statistics = statistics;
                    ApplyFilters();

                    System.Diagnostics.Debug.WriteLine($"🔍 بعد الفلترة: {FilteredDrivers.Count} سائق في FilteredDrivers");

                    LastUpdateTime = DateTime.Now.ToString("HH:mm:ss");
                    OnPropertyChanged(nameof(LastUpdateTime));
                });

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {activeDrivers.Count} سائق نشط - عدد المفلترين: {FilteredDrivers.Count}");

                // طباعة تفاصيل أول سائق للتشخيص
                if (FilteredDrivers.Count > 0)
                {
                    var firstDriver = FilteredDrivers[0];
                    System.Diagnostics.Debug.WriteLine($"🔍 أول سائق: {firstDriver.DriverName}");
                    System.Diagnostics.Debug.WriteLine($"   📋 المهمة: {firstDriver.MissionPurpose}");
                    System.Diagnostics.Debug.WriteLine($"   📅 التواريخ: {firstDriver.DepartureDateText} - {firstDriver.ReturnDateText}");
                    System.Diagnostics.Debug.WriteLine($"   🎯 المشاريع: {firstDriver.ProjectsText}");
                    System.Diagnostics.Debug.WriteLine($"   👥 القائمين: {firstDriver.VisitorsText}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الوصول السريع: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
            finally
            {
                IsLoading = false;
                System.Diagnostics.Debug.WriteLine($"🏁 انتهاء التحميل - IsLoading = {IsLoading}");
            }
        }

        /// <summary>
        /// تطبيق الفلاتر على البيانات
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تطبيق الفلاتر على {ActiveDrivers.Count} سائق");
                System.Diagnostics.Debug.WriteLine($"🔍 إعدادات الفلتر: ShowOnlyInField={_filter.ShowOnlyInField}, SearchText='{_filter.SearchText}'");

                var filtered = _quickAccessService.FilterActiveDrivers(ActiveDrivers.ToList(), _filter);

                System.Diagnostics.Debug.WriteLine($"🔍 نتيجة الفلترة: {filtered.Count} سائق");

                FilteredDrivers.Clear();
                foreach (var driver in filtered)
                {
                    FilteredDrivers.Add(driver);
                    System.Diagnostics.Debug.WriteLine($"🔍 إضافة سائق مفلتر: {driver.DriverName} - الحالة: {driver.Status}");
                }

                System.Diagnostics.Debug.WriteLine($"🔍 تم فلترة {filtered.Count} من أصل {ActiveDrivers.Count} سائق");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        /// <summary>
        /// مسح جميع الفلاتر
        /// </summary>
        private void ClearFilters()
        {
            SearchText = string.Empty;
            ShowOnlyInField = true;
            ShowEndingToday = false;
            ShowEndingTomorrow = false;
            _filter.FilterDate = null;
        }

        /// <summary>
        /// تحديد جميع السائقين
        /// </summary>
        private void SelectAll()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = true;
            }
        }

        /// <summary>
        /// إلغاء تحديد جميع السائقين
        /// </summary>
        private void ClearSelection()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = false;
            }
        }

        /// <summary>
        /// إعداد التحديث التلقائي كل 5 دقائق
        /// </summary>
        private void InitializeAutoRefresh()
        {
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5) // تحديث كل 5 دقائق
            };
            
            _refreshTimer.Tick += async (sender, e) => await LoadDataAsync();
            _refreshTimer.Start();
            
            System.Diagnostics.Debug.WriteLine("⏰ تم تفعيل التحديث التلقائي كل 5 دقائق");
        }

        /// <summary>
        /// تبديل التبويبات
        /// </summary>
        private void SwitchTab(string tabName)
        {
            SelectedTab = tabName;
            System.Diagnostics.Debug.WriteLine($"🔄 تم التبديل إلى تبويب: {tabName}");
        }

        /// <summary>
        /// تحديث رؤية التبويبات
        /// </summary>
        private void UpdateTabVisibility()
        {
            IsDashboardVisible = SelectedTab == "Dashboard";
            IsQuickAccessVisible = SelectedTab == "QuickAccess";
        }

        /// <summary>
        /// تهيئة المخططات البيانية
        /// </summary>
        private void InitializeCharts()
        {
            CreateMonthlyVisitsChart();
            CreateDriversStatusChart();
        }

        /// <summary>
        /// إنشاء مخطط الزيارات الشهرية
        /// </summary>
        private void CreateMonthlyVisitsChart()
        {
            var plotModel = new PlotModel
            {
                Title = "الزيارات الشهرية",
                TitleFontSize = 16,
                Background = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColors.LightGray,
                TextColor = OxyColors.Black
            };

            // إضافة محاور
            var categoryAxis = new CategoryAxis
            {
                Position = AxisPosition.Bottom,
                Title = "الأشهر",
                TitleFontSize = 12,
                FontSize = 10,
                TextColor = OxyColors.Black
            };
            categoryAxis.Labels.AddRange(new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" });

            var valueAxis = new LinearAxis
            {
                Position = AxisPosition.Left,
                Title = "عدد الزيارات",
                TitleFontSize = 12,
                FontSize = 10,
                TextColor = OxyColors.Black,
                Minimum = 0
            };

            plotModel.Axes.Add(categoryAxis);
            plotModel.Axes.Add(valueAxis);

            // إضافة البيانات
            var barSeries = new BarSeries
            {
                Title = "الزيارات",
                FillColor = OxyColor.FromRgb(33, 150, 243), // #2196F3
                StrokeColor = OxyColors.White,
                StrokeThickness = 1
            };

            // بيانات تجريبية
            barSeries.Items.Add(new BarItem { Value = 25 });
            barSeries.Items.Add(new BarItem { Value = 35 });
            barSeries.Items.Add(new BarItem { Value = 18 });
            barSeries.Items.Add(new BarItem { Value = 42 });
            barSeries.Items.Add(new BarItem { Value = 28 });
            barSeries.Items.Add(new BarItem { Value = 38 });

            plotModel.Series.Add(barSeries);
            MonthlyVisitsChart = plotModel;
        }

        /// <summary>
        /// إنشاء مخطط حالة السائقين
        /// </summary>
        private void CreateDriversStatusChart()
        {
            var plotModel = new PlotModel
            {
                Title = "حالة السائقين",
                TitleFontSize = 16,
                Background = OxyColors.Transparent,
                PlotAreaBorderColor = OxyColors.Transparent,
                TextColor = OxyColors.Black
            };

            var pieSeries = new PieSeries
            {
                StrokeThickness = 2.0,
                InsideLabelPosition = 0.8,
                AngleSpan = 360,
                StartAngle = 0,
                FontSize = 12
            };

            // إضافة البيانات
            pieSeries.Slices.Add(new PieSlice("في الميدان", 45) { Fill = OxyColor.FromRgb(33, 150, 243) }); // #2196F3
            pieSeries.Slices.Add(new PieSlice("في المكتب", 25) { Fill = OxyColor.FromRgb(76, 175, 80) }); // #4CAF50
            pieSeries.Slices.Add(new PieSlice("في إجازة", 20) { Fill = OxyColor.FromRgb(255, 152, 0) }); // #FF9800
            pieSeries.Slices.Add(new PieSlice("غير متاح", 10) { Fill = OxyColor.FromRgb(233, 30, 99) }); // #E91E63

            plotModel.Series.Add(pieSeries);
            DriversStatusChart = plotModel;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _quickAccessService?.Dispose();
        }

        #endregion
    }
}
