using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// محول لتحديد ستايل التبويب النشط
    /// </summary>
    public class TabStyleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return Application.Current.FindResource("TabButtonStyle");

            string selectedTab = value.ToString();
            string tabName = parameter.ToString();

            if (selectedTab == tabName)
                return Application.Current.FindResource("ActiveTabButtonStyle");
            else
                return Application.Current.FindResource("TabButtonStyle");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
